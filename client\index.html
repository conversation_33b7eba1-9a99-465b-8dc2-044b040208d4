<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Colyseus + Phaser Example</title>
    <style>
        * { margin: 0; padding: 0;}
        html, body { font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif; }

        a { color: red; }
        input { outline: none; }

        div.menu { padding: 1em; }
        div.menu > div { margin-bottom: 0.1em; }
    </style>
</head>
<body>
    <div class="menu">
        <div>
            <label for="fps">FPS:</label>
            <input type="range" min="8" max="60" value="60" id="fps">
            <span id="fps-value">60</span>
        </div>

        <div>
            <label for="latency">Latency Simulation:</label>
            <input type="range" min="0" max="500" value="0" id="latency">
            <span id="latency-value">0 ms</span>
        </div>

        <div>
            <a href="/" class="button">Back to menu</a>
        </div>
    </div>

    <script src="./src/main.js" type="module"></script>


</body>
</html>
