{"name": "client", "version": "1.0.0", "type": "module", "description": "Colyseus + Phaser: Client-side", "scripts": {"start": "vite", "dev": "vite", "build": "tsc --noEmit && vite build", "preview": "vite preview"}, "keywords": ["discord", "embedded", "colyseus"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/colyseus/discord-embedded-app-sdk/issues"}, "homepage": "https://github.com/colyseus/discord-embedded-app-sdk#readme", "devDependencies": {"cloudflared": "^0.5.1", "typescript": "^5.2.2", "vite": "^5.2.10"}, "dependencies": {"@coreui/coreui": "^4.3.0", "@coreui/vue": "^4.10.0", "@discord/embedded-app-sdk": "^1.2.0", "@grapoza/vue-tree": "^5.2.0", "@pixi/tilemap": "^5.0.1", "@popperjs/core": "^2.11.8", "@tweenjs/tween.js": "^23.1.1", "@vitejs/plugin-vue": "^5.1.2", "axios": "1.3.4", "bootstrap": "^5.3.1", "buffer": "5.7.1", "carbon-components": "^10.58.8", "colyseus.js": "^0.15.19", "d3": "^7.8.5", "d3-flextree": "^2.1.2", "d3-org-chart": "^2.7.0", "gauge": "^5.0.1", "phaser": "^3.8", "phaser-animated-tiles": "^2.0.2", "phaser3-rex-plugins": "^1.8", "pinia": "2.0.33", "pinia-plugin-persistedstate": "^3.2.1", "pixi.js": "^8.1.0", "process": "^0.11.10", "sass": "^1.64.2", "vue": "^3.3.4", "vue-gauge": "^1.0.3", "vue-loader": "^17.2.2", "vue-template-compiler": "^2.7.14", "vuedraggable": "^4.1.0"}}