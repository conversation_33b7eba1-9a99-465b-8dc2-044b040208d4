langcode: en
status: true
dependencies:
  config:
    - field.field.node.dialog.body
    - field.field.node.dialog.field_dialog_line
    - node.type.dialog
  module:
    - paragraphs
    - path
    - text
id: node.dialog.default
targetEntityType: node
bundle: dialog
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 121
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_dialog_line:
    type: paragraphs
    weight: 122
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
