langcode: en
status: true
dependencies:
  config:
    - field.field.node.game.body
    - field.field.node.game.field_debug
    - node.type.game
  module:
    - path
    - text
id: node.game.default
targetEntityType: node
bundle: game
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 101
    region: content
    settings: {  }
    third_party_settings: {  }
    label: hidden
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_debug:
    type: boolean_checkbox
    weight: 121
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  links:
    weight: 100
    region: content
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
