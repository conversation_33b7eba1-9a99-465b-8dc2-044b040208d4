langcode: en
status: true
dependencies:
  config:
    - field.field.node.map_grid.body
    - field.field.node.map_grid.field_city
    - field.field.node.map_grid.field_folio
    - field.field.node.map_grid.field_layer_1
    - field.field.node.map_grid.field_layer_2
    - field.field.node.map_grid.field_layer_3
    - field.field.node.map_grid.field_layer_4
    - field.field.node.map_grid.field_layer_5
    - field.field.node.map_grid.field_map_height
    - field.field.node.map_grid.field_map_width
    - field.field.node.map_grid.field_mobs
    - field.field.node.map_grid.field_npc
    - field.field.node.map_grid.field_portals
    - field.field.node.map_grid.field_rewards
    - field.field.node.map_grid.field_soundtrack
    - field.field.node.map_grid.field_switches
    - field.field.node.map_grid.field_tiled
    - field.field.node.map_grid.field_walls
    - node.type.map_grid
  module:
    - paragraphs
    - path
    - text
id: node.map_grid.default
targetEntityType: node
bundle: map_grid
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 6
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
    label: hidden
  created:
    type: datetime_timestamp
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_city:
    type: entity_reference_autocomplete
    weight: 11
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_folio:
    type: paragraphs
    weight: 103
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_layer_1:
    type: options_buttons
    weight: 13
    region: content
    settings: {  }
    third_party_settings: {  }
  field_layer_2:
    type: options_buttons
    weight: 14
    region: content
    settings: {  }
    third_party_settings: {  }
  field_layer_3:
    type: options_buttons
    weight: 15
    region: content
    settings: {  }
    third_party_settings: {  }
  field_layer_4:
    type: options_buttons
    weight: 16
    region: content
    settings: {  }
    third_party_settings: {  }
  field_layer_5:
    type: options_buttons
    weight: 17
    region: content
    settings: {  }
    third_party_settings: {  }
  field_map_height:
    type: number
    weight: 9
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_map_width:
    type: number
    weight: 10
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_mobs:
    type: paragraphs
    weight: 20
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: mobs
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_npc:
    type: paragraphs
    weight: 18
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: npc
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_portals:
    type: paragraphs
    weight: 12
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: portal
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_rewards:
    type: paragraphs
    weight: 19
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: rewards
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_soundtrack:
    type: entity_reference_autocomplete
    weight: 104
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_switches:
    type: paragraphs
    weight: 102
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_tiled:
    type: number
    weight: 8
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_walls:
    type: paragraphs
    weight: 101
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: walls
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  links:
    weight: 100
    region: content
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 1
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
