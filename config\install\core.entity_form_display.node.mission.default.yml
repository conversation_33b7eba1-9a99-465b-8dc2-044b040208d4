langcode: en
status: true
dependencies:
  config:
    - field.field.node.mission.body
    - field.field.node.mission.field_choice_a
    - field.field.node.mission.field_handler_dialog
    - field.field.node.mission.field_quest_item
    - field.field.node.mission.field_reward_item
    - node.type.mission
  module:
    - path
    - text
id: node.mission.default
targetEntityType: node
bundle: mission
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 121
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_choice_a:
    type: string_textfield
    weight: 126
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_handler_dialog:
    type: string_textarea
    weight: 127
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_quest_item:
    type: entity_reference_autocomplete
    weight: 124
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_reward_item:
    type: entity_reference_autocomplete
    weight: 125
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
