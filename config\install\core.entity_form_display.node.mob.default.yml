langcode: en
status: true
dependencies:
  config:
    - field.field.node.mob.body
    - field.field.node.mob.field_mob_sprite_sheet
    - field.field.node.mob.field_type
    - node.type.mob
  module:
    - path
    - text
id: node.mob.default
targetEntityType: node
bundle: mob
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 7
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_mob_sprite_sheet:
    type: number
    weight: 9
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_type:
    type: options_select
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 1
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
