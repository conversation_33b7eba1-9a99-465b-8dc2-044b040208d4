langcode: en
status: true
dependencies:
  config:
    - field.field.node.npc.body
    - field.field.node.npc.field_bark
    - field.field.node.npc.field_concerns
    - field.field.node.npc.field_faction
    - field.field.node.npc.field_is_handler
    - field.field.node.npc.field_missions
    - field.field.node.npc.field_occupation
    - field.field.node.npc.field_race
    - field.field.node.npc.field_sprite_sheet
    - node.type.npc
  module:
    - path
    - text
id: node.npc.default
targetEntityType: node
bundle: npc
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 7
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_bark:
    type: string_textarea
    weight: 12
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_concerns:
    type: options_select
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  field_faction:
    type: entity_reference_autocomplete
    weight: 11
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_is_handler:
    type: options_buttons
    weight: 15
    region: content
    settings: {  }
    third_party_settings: {  }
  field_missions:
    type: entity_reference_autocomplete
    weight: 14
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_occupation:
    type: options_select
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  field_race:
    type: options_select
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_sprite_sheet:
    type: number
    weight: 13
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 1
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
