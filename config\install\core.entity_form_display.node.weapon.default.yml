langcode: en
status: true
dependencies:
  config:
    - field.field.node.weapon.body
    - field.field.node.weapon.field_ammo_type
    - field.field.node.weapon.field_magazine
    - field.field.node.weapon.field_modifiers
    - node.type.weapon
  module:
    - paragraphs
    - path
    - text
id: node.weapon.default
targetEntityType: node
bundle: weapon
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 7
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_ammo_type:
    type: options_select
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  field_magazine:
    type: number
    weight: 9
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_modifiers:
    type: paragraphs
    weight: 11
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 1
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
