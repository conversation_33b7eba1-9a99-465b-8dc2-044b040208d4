langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.mission_stage.field_handler
    - field.field.paragraph.mission_stage.field_handler_dialogue
    - field.field.paragraph.mission_stage.field_mark
    - field.field.paragraph.mission_stage.field_mark_dialog
    - field.field.paragraph.mission_stage.field_mark_grid_map
    - field.field.paragraph.mission_stage.field_prerequisite
    - field.field.paragraph.mission_stage.field_reward
    - field.field.paragraph.mission_stage.field_stage
    - field.field.paragraph.mission_stage.field_x
    - field.field.paragraph.mission_stage.field_y
    - paragraphs.paragraphs_type.mission_stage
id: paragraph.mission_stage.default
targetEntityType: paragraph
bundle: mission_stage
mode: default
content:
  field_handler:
    type: entity_reference_autocomplete
    weight: 1
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_handler_dialogue:
    type: string_textarea
    weight: 2
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_mark:
    type: entity_reference_autocomplete
    weight: 3
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_mark_dialog:
    type: string_textarea
    weight: 5
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_mark_grid_map:
    type: entity_reference_autocomplete
    weight: 4
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_prerequisite:
    type: entity_reference_autocomplete
    weight: 8
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_reward:
    type: entity_reference_autocomplete
    weight: 9
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_stage:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_x:
    type: number
    weight: 6
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_y:
    type: number
    weight: 7
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  status: true
