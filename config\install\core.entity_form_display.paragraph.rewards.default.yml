langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.rewards.field_ref
    - field.field.paragraph.rewards.field_reward_item
    - field.field.paragraph.rewards.field_x
    - field.field.paragraph.rewards.field_y
    - paragraphs.paragraphs_type.rewards
  module:
    - paragraphs
id: paragraph.rewards.default
targetEntityType: paragraph
bundle: rewards
mode: default
content:
  field_ref:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_reward_item:
    type: paragraphs
    weight: 3
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_x:
    type: number
    weight: 1
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_y:
    type: number
    weight: 2
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  status: true
