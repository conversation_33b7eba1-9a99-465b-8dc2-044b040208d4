langcode: en
status: true
dependencies:
  config:
    - core.entity_form_mode.profile.player
    - field.field.profile.player.field_charisma
    - field.field.profile.player.field_credits
    - field.field.profile.player.field_dexterity
    - field.field.profile.player.field_endurance
    - field.field.profile.player.field_energy
    - field.field.profile.player.field_game_state
    - field.field.profile.player.field_health
    - field.field.profile.player.field_intelligence
    - field.field.profile.player.field_inventory
    - field.field.profile.player.field_left_weapon
    - field.field.profile.player.field_level
    - field.field.profile.player.field_losses
    - field.field.profile.player.field_map
    - field.field.profile.player.field_map_grid
    - field.field.profile.player.field_missions
    - field.field.profile.player.field_missions_completed
    - field.field.profile.player.field_nanites
    - field.field.profile.player.field_psi
    - field.field.profile.player.field_right_weapon
    - field.field.profile.player.field_skill
    - field.field.profile.player.field_strength
    - field.field.profile.player.field_wins
    - field.field.profile.player.field_x
    - field.field.profile.player.field_xp
    - field.field.profile.player.field_y
    - profile.type.player
  module:
    - paragraphs
id: profile.player.player
targetEntityType: profile
bundle: player
mode: player
content:
  field_charisma:
    type: number
    weight: 10
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_credits:
    type: number
    weight: 13
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_dexterity:
    type: number
    weight: 8
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_endurance:
    type: number
    weight: 9
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_energy:
    type: number
    weight: 5
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_game_state:
    type: options_select
    weight: 24
    region: content
    settings: {  }
    third_party_settings: {  }
  field_health:
    type: number
    weight: 4
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_intelligence:
    type: number
    weight: 6
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_inventory:
    type: paragraphs
    weight: 22
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_left_weapon:
    type: number
    weight: 16
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_level:
    type: number
    weight: 15
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_losses:
    type: number
    weight: 19
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_map:
    type: number
    weight: 25
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_map_grid:
    type: entity_reference_autocomplete
    weight: 1
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_nanites:
    type: number
    weight: 14
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_psi:
    type: number
    weight: 11
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_right_weapon:
    type: number
    weight: 17
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_skill:
    type: paragraphs
    weight: 20
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: skill
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_strength:
    type: number
    weight: 7
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_wins:
    type: number
    weight: 18
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_x:
    type: number
    weight: 2
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_xp:
    type: number
    weight: 12
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_y:
    type: number
    weight: 3
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
hidden:
  field_missions: true
  field_missions_completed: true
  is_default: true
