langcode: en
status: true
dependencies:
  config:
    - core.entity_form_mode.user.register
    - field.field.user.user.field_game_state
    - field.field.user.user.field_sprite_sheet
    - field.field.user.user.user_picture
  module:
    - user
id: user.user.register
targetEntityType: user
bundle: user
mode: register
content:
  account:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_sprite_sheet:
    type: options_select
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  language:
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  timezone:
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  contact: true
  faction_profiles: true
  field_game_state: true
  path: true
  player_profiles: true
  user_picture: true
