langcode: en
status: true
dependencies:
  config:
    - field.field.node.dialog.body
    - field.field.node.dialog.field_dialog_line
    - node.type.dialog
  module:
    - entity_reference_revisions
    - text
    - user
id: node.dialog.default
targetEntityType: node
bundle: dialog
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  field_dialog_line:
    type: entity_reference_revisions_entity_view
    label: hidden
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 1
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
hidden: {  }
