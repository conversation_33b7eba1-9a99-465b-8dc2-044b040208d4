langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.dialog.body
    - field.field.node.dialog.field_dialog_line
    - node.type.dialog
  module:
    - text
    - user
id: node.dialog.teaser
targetEntityType: node
bundle: dialog
mode: teaser
content:
  body:
    type: text_summary_or_trimmed
    label: hidden
    settings:
      trim_length: 600
    third_party_settings: {  }
    weight: 101
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  field_dialog_line: true
