langcode: en
status: true
dependencies:
  config:
    - field.field.node.faction.body
    - field.field.node.faction.field_faction_type
    - node.type.faction
  module:
    - text
    - user
id: node.faction.default
targetEntityType: node
bundle: faction
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  field_faction_type:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 1
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
hidden: {  }
