langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.faction.body
    - field.field.node.faction.field_faction_type
    - node.type.faction
  module:
    - text
    - user
id: node.faction.teaser
targetEntityType: node
bundle: faction
mode: teaser
content:
  body:
    type: text_summary_or_trimmed
    label: hidden
    settings:
      trim_length: 600
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  field_faction_type: true
  links: true
