langcode: en
status: true
dependencies:
  config:
    - field.field.node.game.body
    - field.field.node.game.field_debug
    - node.type.game
  module:
    - text
    - user
id: node.game.default
targetEntityType: node
bundle: game
mode: default
content:
  body:
    type: text_summary_or_trimmed
    label: hidden
    settings:
      trim_length: 600
    third_party_settings: {  }
    weight: 1
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  field_debug: true
