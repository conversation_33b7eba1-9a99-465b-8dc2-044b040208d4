langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.game.body
    - field.field.node.game.field_debug
    - node.type.game
  module:
    - text
    - user
id: node.game.teaser
targetEntityType: node
bundle: game
mode: teaser
content:
  body:
    type: text_summary_or_trimmed
    label: hidden
    settings:
      trim_length: 600
    third_party_settings: {  }
    weight: 101
    region: content
  links:
    weight: 100
    region: content
hidden:
  field_debug: true
