langcode: en
status: true
dependencies:
  config:
    - field.field.node.meme.body
    - node.type.meme
  module:
    - text
    - user
id: node.meme.default
targetEntityType: node
bundle: meme
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 101
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden: {  }
