langcode: en
status: true
dependencies:
  config:
    - field.field.node.mission.body
    - field.field.node.mission.field_choice_a
    - field.field.node.mission.field_handler_dialog
    - field.field.node.mission.field_quest_item
    - field.field.node.mission.field_reward_item
    - node.type.mission
  module:
    - text
    - user
id: node.mission.default
targetEntityType: node
bundle: mission
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 101
    region: content
  field_choice_a:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 106
    region: content
  field_handler_dialog:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 107
    region: content
  field_quest_item:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 104
    region: content
  field_reward_item:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 105
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden: {  }
