langcode: en
status: true
dependencies:
  config:
    - field.field.node.npc.body
    - field.field.node.npc.field_bark
    - field.field.node.npc.field_concerns
    - field.field.node.npc.field_faction
    - field.field.node.npc.field_is_handler
    - field.field.node.npc.field_missions
    - field.field.node.npc.field_occupation
    - field.field.node.npc.field_race
    - field.field.node.npc.field_sprite_sheet
    - node.type.npc
  module:
    - text
    - user
id: node.npc.default
targetEntityType: node
bundle: npc
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 101
    region: content
  field_bark:
    type: basic_string
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 110
    region: content
  field_concerns:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 115
    region: content
  field_faction:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 109
    region: content
  field_is_handler:
    type: boolean
    label: inline
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 113
    region: content
  field_missions:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 112
    region: content
  field_occupation:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 114
    region: content
  field_race:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 116
    region: content
  field_sprite_sheet:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 111
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden: {  }
