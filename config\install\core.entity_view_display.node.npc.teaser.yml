langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.npc.body
    - field.field.node.npc.field_bark
    - field.field.node.npc.field_concerns
    - field.field.node.npc.field_faction
    - field.field.node.npc.field_is_handler
    - field.field.node.npc.field_missions
    - field.field.node.npc.field_occupation
    - field.field.node.npc.field_race
    - field.field.node.npc.field_sprite_sheet
    - node.type.npc
  module:
    - text
    - user
id: node.npc.teaser
targetEntityType: node
bundle: npc
mode: teaser
content:
  body:
    type: text_summary_or_trimmed
    label: hidden
    settings:
      trim_length: 600
    third_party_settings: {  }
    weight: 101
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  field_bark: true
  field_concerns: true
  field_faction: true
  field_is_handler: true
  field_missions: true
  field_occupation: true
  field_race: true
  field_sprite_sheet: true
