langcode: en
status: true
dependencies:
  config:
    - field.field.node.skill.body
    - node.type.skill
  module:
    - text
    - user
id: node.skill.default
targetEntityType: node
bundle: skill
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 101
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden: {  }
