langcode: en
status: true
dependencies:
  config:
    - field.field.node.soundtrack.body
    - field.field.node.soundtrack.field_composer
    - field.field.node.soundtrack.field_track
    - node.type.soundtrack
  module:
    - text
    - user
id: node.soundtrack.default
targetEntityType: node
bundle: soundtrack
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 101
    region: content
  field_composer:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 102
    region: content
  field_track:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 103
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden: {  }
