langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.buildings.field_building_name
    - field.field.paragraph.buildings.field_building_type
    - paragraphs.paragraphs_type.buildings
id: paragraph.buildings.default
targetEntityType: paragraph
bundle: buildings
mode: default
content:
  field_building_name:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_building_type:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 2
    region: content
hidden: {  }
