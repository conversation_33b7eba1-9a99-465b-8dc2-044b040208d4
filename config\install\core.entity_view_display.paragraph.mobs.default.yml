langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.mobs.field_level
    - field.field.paragraph.mobs.field_mob_name
    - field.field.paragraph.mobs.field_mobs
    - field.field.paragraph.mobs.field_x
    - field.field.paragraph.mobs.field_y
    - paragraphs.paragraphs_type.mobs
id: paragraph.mobs.default
targetEntityType: paragraph
bundle: mobs
mode: default
content:
  field_level:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 4
    region: content
  field_mob_name:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_mobs:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 0
    region: content
  field_x:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 1
    region: content
  field_y:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 2
    region: content
hidden: {  }
