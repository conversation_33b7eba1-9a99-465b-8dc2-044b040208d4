langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.npc.field_name
    - field.field.paragraph.npc.field_x
    - field.field.paragraph.npc.field_y
    - paragraphs.paragraphs_type.npc
id: paragraph.npc.default
targetEntityType: paragraph
bundle: npc
mode: default
content:
  field_name:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 0
    region: content
  field_x:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 1
    region: content
  field_y:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 2
    region: content
hidden: {  }
