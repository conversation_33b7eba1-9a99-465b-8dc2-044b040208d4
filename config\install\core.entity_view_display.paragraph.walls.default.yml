langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.walls.field_height
    - field.field.paragraph.walls.field_width
    - field.field.paragraph.walls.field_x
    - field.field.paragraph.walls.field_y
    - paragraphs.paragraphs_type.walls
id: paragraph.walls.default
targetEntityType: paragraph
bundle: walls
mode: default
content:
  field_height:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 7
    region: content
  field_width:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 6
    region: content
  field_x:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 4
    region: content
  field_y:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 5
    region: content
hidden: {  }
