langcode: en
status: true
dependencies:
  config:
    - field.field.profile.player.field_charisma
    - field.field.profile.player.field_credits
    - field.field.profile.player.field_dexterity
    - field.field.profile.player.field_endurance
    - field.field.profile.player.field_energy
    - field.field.profile.player.field_game_state
    - field.field.profile.player.field_health
    - field.field.profile.player.field_intelligence
    - field.field.profile.player.field_inventory
    - field.field.profile.player.field_left_weapon
    - field.field.profile.player.field_level
    - field.field.profile.player.field_losses
    - field.field.profile.player.field_map
    - field.field.profile.player.field_map_grid
    - field.field.profile.player.field_missions
    - field.field.profile.player.field_missions_completed
    - field.field.profile.player.field_nanites
    - field.field.profile.player.field_psi
    - field.field.profile.player.field_right_weapon
    - field.field.profile.player.field_skill
    - field.field.profile.player.field_strength
    - field.field.profile.player.field_wins
    - field.field.profile.player.field_x
    - field.field.profile.player.field_xp
    - field.field.profile.player.field_y
    - profile.type.player
  module:
    - entity_reference_revisions
    - options
id: profile.player.default
targetEntityType: profile
bundle: player
mode: default
content:
  field_charisma:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 10
    region: content
  field_credits:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 13
    region: content
  field_dexterity:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 8
    region: content
  field_endurance:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 9
    region: content
  field_energy:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 5
    region: content
  field_game_state:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 24
    region: content
  field_health:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 4
    region: content
  field_intelligence:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 6
    region: content
  field_inventory:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 22
    region: content
  field_left_weapon:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 16
    region: content
  field_level:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 15
    region: content
  field_losses:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 19
    region: content
  field_map:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 25
    region: content
  field_map_grid:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 1
    region: content
  field_missions:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 26
    region: content
  field_missions_completed:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 27
    region: content
  field_nanites:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 14
    region: content
  field_psi:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 11
    region: content
  field_right_weapon:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 17
    region: content
  field_skill:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 20
    region: content
  field_strength:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 7
    region: content
  field_wins:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 18
    region: content
  field_x:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 2
    region: content
  field_xp:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 12
    region: content
  field_y:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 3
    region: content
hidden: {  }
