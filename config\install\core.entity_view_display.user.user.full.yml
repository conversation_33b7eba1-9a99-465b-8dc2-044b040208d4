langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.user.full
    - field.field.user.user.field_game_state
    - field.field.user.user.field_sprite_sheet
    - field.field.user.user.user_picture
  module:
    - user
id: user.user.full
targetEntityType: user
bundle: user
mode: full
content:
  faction_profiles:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: true
    third_party_settings: {  }
    weight: 2
    region: content
  member_for:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  player_profiles:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: player
      link: false
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  field_game_state: true
  field_sprite_sheet: true
  user_picture: true
