langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_city_description
    - node.type.city
  module:
    - text
id: node.city.field_city_description
field_name: field_city_description
entity_type: node
bundle: city
label: Description
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  display_summary: false
  required_summary: false
field_type: text_with_summary
