langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_city_education_level
    - node.type.city
  module:
    - options
id: node.city.field_city_education_level
field_name: field_city_education_level
entity_type: node
bundle: city
label: 'City Education Level'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_integer
