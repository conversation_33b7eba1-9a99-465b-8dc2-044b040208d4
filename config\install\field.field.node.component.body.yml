langcode: en
status: true
dependencies:
  config:
    - field.storage.node.body
    - node.type.component
  module:
    - text
id: node.component.body
field_name: body
entity_type: node
bundle: component
label: Body
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: true
  required_summary: false
field_type: text_with_summary
