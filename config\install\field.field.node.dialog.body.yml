langcode: en
status: true
dependencies:
  config:
    - field.storage.node.body
    - node.type.dialog
  module:
    - text
id: node.dialog.body
field_name: body
entity_type: node
bundle: dialog
label: Body
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: true
  required_summary: false
  allowed_formats: {  }
field_type: text_with_summary
