langcode: en
status: true
dependencies:
  config:
    - field.storage.node.body
    - node.type.game
  module:
    - text
id: node.game.body
field_name: body
entity_type: node
bundle: game
label: Body
description: 'More specific information about the Game.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: true
  required_summary: false
field_type: text_with_summary
