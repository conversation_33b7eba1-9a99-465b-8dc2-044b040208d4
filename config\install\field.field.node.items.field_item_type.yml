langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_item_type
    - node.type.items
    - taxonomy.vocabulary.item_type
id: node.items.field_item_type
field_name: field_item_type
entity_type: node
bundle: items
label: 'Item Type'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      item_type: item_type
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
