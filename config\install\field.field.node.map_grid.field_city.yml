langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_city
    - node.type.city
    - node.type.map_grid
id: node.map_grid.field_city
field_name: field_city
entity_type: node
bundle: map_grid
label: City
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      city: city
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
