langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_soundtrack
    - node.type.map_grid
    - node.type.soundtrack
id: node.map_grid.field_soundtrack
field_name: field_soundtrack
entity_type: node
bundle: map_grid
label: Soundtrack
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      soundtrack: soundtrack
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
