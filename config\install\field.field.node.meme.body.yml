langcode: en
status: true
dependencies:
  config:
    - field.storage.node.body
    - node.type.meme
  module:
    - text
id: node.meme.body
field_name: body
entity_type: node
bundle: meme
label: Body
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: true
  required_summary: false
  allowed_formats: {  }
field_type: text_with_summary
