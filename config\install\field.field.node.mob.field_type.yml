langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_type
    - node.type.mob
    - taxonomy.vocabulary.mob_type
id: node.mob.field_type
field_name: field_type
entity_type: node
bundle: mob
label: Type
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      mob_type: mob_type
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
