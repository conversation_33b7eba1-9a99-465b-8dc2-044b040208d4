langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_concerns
    - node.type.npc
    - taxonomy.vocabulary.concerns
id: node.npc.field_concerns
field_name: field_concerns
entity_type: node
bundle: npc
label: Concerns
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      concerns: concerns
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
