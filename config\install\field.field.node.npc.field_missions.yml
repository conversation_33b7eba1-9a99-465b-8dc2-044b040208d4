langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_missions
    - node.type.mission
    - node.type.npc
id: node.npc.field_missions
field_name: field_missions
entity_type: node
bundle: npc
label: Missions
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      mission: mission
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
