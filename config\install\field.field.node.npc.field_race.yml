langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_race
    - node.type.npc
    - taxonomy.vocabulary.race
id: node.npc.field_race
field_name: field_race
entity_type: node
bundle: npc
label: Race
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      race: race
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
