langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_composer
    - node.type.soundtrack
    - taxonomy.vocabulary.composer
id: node.soundtrack.field_composer
field_name: field_composer
entity_type: node
bundle: soundtrack
label: Composer
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      composer: composer
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
