langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_components
    - node.type.component
    - paragraphs.paragraphs_type.components
id: paragraph.components.field_components
field_name: field_components
entity_type: paragraph
bundle: components
label: Component
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      component: component
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
