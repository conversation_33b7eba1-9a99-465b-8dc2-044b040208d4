langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_page
    - node.type.page
    - paragraphs.paragraphs_type.folio
id: paragraph.folio.field_page
field_name: field_page
entity_type: paragraph
bundle: folio
label: Page
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      page: page
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
