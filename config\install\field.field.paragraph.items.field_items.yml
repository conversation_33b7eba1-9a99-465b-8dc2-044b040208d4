langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_items
    - node.type.items
    - paragraphs.paragraphs_type.items
id: paragraph.items.field_items
field_name: field_items
entity_type: paragraph
bundle: items
label: Item
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      items: items
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
