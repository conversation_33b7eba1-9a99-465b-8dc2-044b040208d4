langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_location
    - paragraphs.paragraphs_type.items
  module:
    - options
id: paragraph.items.field_location
field_name: field_location
entity_type: paragraph
bundle: items
label: Location
description: ''
required: true
translatable: false
default_value:
  -
    value: Backpack
default_value_callback: ''
settings: {  }
field_type: list_string
