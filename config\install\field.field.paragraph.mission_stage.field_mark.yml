langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_mark
    - node.type.npc
    - paragraphs.paragraphs_type.mission_stage
id: paragraph.mission_stage.field_mark
field_name: field_mark
entity_type: paragraph
bundle: mission_stage
label: Mark
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      npc: npc
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
