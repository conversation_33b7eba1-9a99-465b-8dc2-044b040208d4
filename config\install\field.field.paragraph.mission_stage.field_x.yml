langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_x
    - paragraphs.paragraphs_type.mission_stage
id: paragraph.mission_stage.field_x
field_name: field_x
entity_type: paragraph
bundle: mission_stage
label: 'Mark X'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: integer
