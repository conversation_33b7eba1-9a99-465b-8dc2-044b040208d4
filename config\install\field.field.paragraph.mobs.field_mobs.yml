langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_mobs
    - node.type.mob
    - paragraphs.paragraphs_type.mobs
id: paragraph.mobs.field_mobs
field_name: field_mobs
entity_type: paragraph
bundle: mobs
label: Mob
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      mob: mob
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
