langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_end_frame
    - paragraphs.paragraphs_type.switches
id: paragraph.switches.field_end_frame
field_name: field_end_frame
entity_type: paragraph
bundle: switches
label: 'End Frame'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: integer
