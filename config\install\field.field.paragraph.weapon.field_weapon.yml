langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_weapon
    - node.type.weapon
    - paragraphs.paragraphs_type.weapon
id: paragraph.weapon.field_weapon
field_name: field_weapon
entity_type: paragraph
bundle: weapon
label: Weapon
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      weapon: weapon
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
