langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_losses
    - profile.type.player
id: profile.player.field_losses
field_name: field_losses
entity_type: profile
bundle: player
label: Losses
description: ''
required: false
translatable: false
default_value:
  -
    value: 1
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: integer
