langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_psi
    - profile.type.player
id: profile.player.field_psi
field_name: field_psi
entity_type: profile
bundle: player
label: PSI
description: ''
required: false
translatable: false
default_value:
  -
    value: 10
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: integer
