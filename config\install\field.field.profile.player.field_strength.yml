langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_strength
    - profile.type.player
id: profile.player.field_strength
field_name: field_strength
entity_type: profile
bundle: player
label: Strength
description: ''
required: false
translatable: false
default_value:
  -
    value: 10
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: integer
