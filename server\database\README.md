# JiGS Database Setup

Este diretório contém os scripts necessários para inicializar o banco de dados do JiGS RPG Engine.

## Pré-requisitos

1. **MySQL Server** rodando localmente
2. **Banco de dados criado** com o nome configurado em `src/services/db.ts`
3. **Usuário MySQL** com permissões adequadas

## Setup Rápido

### 1. Criar o Banco de Dados

Conecte-se ao MySQL e execute:

```sql
CREATE DATABASE jigs_db;
```

### 2. Configurar Credenciais

Verifique/edite o arquivo `src/services/db.ts`:

```typescript
export default {
  host: "localhost",
  user: "root", 
  password: "", // Sua senha do MySQL
  database: "jigs_db",
}
```

### 3. Inicializar o Banco

Execute o script de inicialização:

```bash
cd server
npm run init-db
```

## O que o Script Faz

O script `init.sql` cria as seguintes tabelas essenciais:

### Tabelas Principais

- **`node`** - Tabela principal de entidades (estilo Drupal)
- **`node_field_data`** - Dados dos campos dos nós
- **`node__field_city`** - Relacionamento entre mapas e cidades
- **`node__field_tiled`** - Valores dos mapas Tiled
- **`profile__field_credits`** - Sistema de créditos dos jogadores

### Dados de Exemplo

O script também insere dados de exemplo:
- Uma cidade de teste
- Um mapa de teste (map_grid)
- Um perfil de jogador com créditos

## Verificação

Após executar o script, você deve ver:

```
✅ Database initialized successfully!
🧪 Testing database with sample query...
✅ Test query successful! Found rooms:
   - Room 2: Test City (Tiled: 1)
🎉 JiGS Database initialization complete!
```

## Estrutura Esperada

O JiGS espera uma estrutura de banco de dados similar ao Drupal, onde:

- **Nodes** representam entidades do jogo (cidades, mapas, NPCs, etc.)
- **Fields** armazenam propriedades específicas
- **Relationships** conectam diferentes entidades

## Troubleshooting

### Erro: Database does not exist
```bash
# Conecte-se ao MySQL e execute:
CREATE DATABASE jigs_db;
```

### Erro: Access denied
- Verifique as credenciais em `src/services/db.ts`
- Certifique-se que o usuário tem permissões no banco

### Erro: Connection refused
- Verifique se o MySQL está rodando
- Confirme host e porta (padrão: localhost:3306)

## Próximos Passos

Após inicializar o banco:

1. **Instalar conteúdo demo** (opcional):
   - https://github.com/Techbot/JiGS-demo-content

2. **Instalar assets** (opcional):
   - https://github.com/Techbot/JiGS-demo-assets

3. **Iniciar o servidor**:
   ```bash
   npm start
   ```

## Desenvolvimento

Para desenvolvimento completo, você precisará:

- **Drupal** com módulos: Paragraphs, Flag, Profile
- **Conteúdo completo** do mundo do jogo
- **Assets gráficos** (tilesets, sprites, etc.)

Este setup mínimo permite testar a conectividade básica do servidor.
