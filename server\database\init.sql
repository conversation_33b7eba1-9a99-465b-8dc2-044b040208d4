-- JiGS Database Initialization Script
-- This creates the minimum required tables for the JiGS RPG engine to function

-- <PERSON>reate database (run this separately if needed)
-- CREATE DATABASE jigs_db;
-- USE jigs_db;

-- Core Drupal-style node table
CREATE TABLE IF NOT EXISTS `node` (
  `nid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `vid` int(10) unsigned DEFAULT NULL,
  `type` varchar(32) NOT NULL DEFAULT '',
  `uuid` varchar(128) NOT NULL,
  `langcode` varchar(12) NOT NULL,
  PRIMARY KEY (`nid`),
  UNIQUE KEY `node_field__uuid__value` (`uuid`),
  KEY `node_field__vid` (`vid`),
  KEY `node_field__type` (`type`),
  KEY `node_field__langcode` (`langcode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Node field data table
CREATE TABLE IF NOT EXISTS `node_field_data` (
  `nid` int(10) unsigned NOT NULL,
  `vid` int(10) unsigned NOT NULL,
  `type` varchar(32) NOT NULL,
  `langcode` varchar(12) NOT NULL,
  `title` varchar(255) NOT NULL,
  `uid` int(10) unsigned NOT NULL,
  `status` tinyint(4) NOT NULL,
  `created` int(11) NOT NULL,
  `changed` int(11) NOT NULL,
  `promote` tinyint(4) NOT NULL,
  `sticky` tinyint(4) NOT NULL,
  `default_langcode` tinyint(4) NOT NULL,
  `revision_translation_affected` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`nid`,`langcode`),
  KEY `node__id__default_langcode__langcode` (`nid`,`default_langcode`,`langcode`),
  KEY `node__vid` (`vid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- City field table
CREATE TABLE IF NOT EXISTS `node__field_city` (
  `bundle` varchar(128) NOT NULL DEFAULT '',
  `deleted` tinyint(4) NOT NULL DEFAULT 0,
  `entity_id` int(10) unsigned NOT NULL,
  `revision_id` int(10) unsigned NOT NULL,
  `langcode` varchar(32) NOT NULL DEFAULT '',
  `delta` int(10) unsigned NOT NULL,
  `field_city_target_id` int(10) unsigned NOT NULL,
  PRIMARY KEY (`entity_id`,`deleted`,`delta`,`langcode`),
  KEY `bundle` (`bundle`),
  KEY `revision_id` (`revision_id`),
  KEY `field_city_target_id` (`field_city_target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tiled field table
CREATE TABLE IF NOT EXISTS `node__field_tiled` (
  `bundle` varchar(128) NOT NULL DEFAULT '',
  `deleted` tinyint(4) NOT NULL DEFAULT 0,
  `entity_id` int(10) unsigned NOT NULL,
  `revision_id` int(10) unsigned NOT NULL,
  `langcode` varchar(32) NOT NULL DEFAULT '',
  `delta` int(10) unsigned NOT NULL,
  `field_tiled_value` int(11) NOT NULL,
  PRIMARY KEY (`entity_id`,`deleted`,`delta`,`langcode`),
  KEY `bundle` (`bundle`),
  KEY `revision_id` (`revision_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Profile credits table (for banking system)
CREATE TABLE IF NOT EXISTS `profile__field_credits` (
  `bundle` varchar(128) NOT NULL DEFAULT '',
  `deleted` tinyint(4) NOT NULL DEFAULT 0,
  `entity_id` int(10) unsigned NOT NULL,
  `revision_id` int(10) unsigned NOT NULL,
  `langcode` varchar(32) NOT NULL DEFAULT '',
  `delta` int(10) unsigned NOT NULL,
  `field_credits_value` decimal(14,4) NOT NULL DEFAULT 0.0000,
  PRIMARY KEY (`entity_id`,`deleted`,`delta`,`langcode`),
  KEY `bundle` (`bundle`),
  KEY `revision_id` (`revision_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing
-- Sample city node
INSERT IGNORE INTO `node` (`nid`, `vid`, `type`, `uuid`, `langcode`) VALUES
(1, 1, 'city', 'city-uuid-1', 'en');

INSERT IGNORE INTO `node_field_data` (`nid`, `vid`, `type`, `langcode`, `title`, `uid`, `status`, `created`, `changed`, `promote`, `sticky`, `default_langcode`) VALUES
(1, 1, 'city', 'en', 'Test City', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 0, 1);

-- Sample map_grid node
INSERT IGNORE INTO `node` (`nid`, `vid`, `type`, `uuid`, `langcode`) VALUES
(2, 2, 'map_grid', 'map-grid-uuid-1', 'en');

INSERT IGNORE INTO `node_field_data` (`nid`, `vid`, `type`, `langcode`, `title`, `uid`, `status`, `created`, `changed`, `promote`, `sticky`, `default_langcode`) VALUES
(2, 2, 'map_grid', 'en', 'Test Map Grid', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 0, 1);

-- Link map_grid to city
INSERT IGNORE INTO `node__field_city` (`bundle`, `deleted`, `entity_id`, `revision_id`, `langcode`, `delta`, `field_city_target_id`) VALUES
('map_grid', 0, 2, 2, 'en', 0, 1);

-- Add tiled value to map_grid
INSERT IGNORE INTO `node__field_tiled` (`bundle`, `deleted`, `entity_id`, `revision_id`, `langcode`, `delta`, `field_tiled_value`) VALUES
('map_grid', 0, 2, 2, 'en', 0, 1);

-- Sample profile with credits
INSERT IGNORE INTO `profile__field_credits` (`bundle`, `deleted`, `entity_id`, `revision_id`, `langcode`, `delta`, `field_credits_value`) VALUES
('player', 0, 1, 1, 'en', 0, 100.0000);

-- Show what was created
SELECT 'Database initialized successfully!' as status;
SELECT 'Sample data:' as info;
SELECT n.nid, n.type, nfd.title FROM node n 
LEFT JOIN node_field_data nfd ON n.nid = nfd.nid;
