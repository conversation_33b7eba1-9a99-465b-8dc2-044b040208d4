{"private": true, "name": "my-app", "version": "1.0.0", "description": "npm init template for bootstrapping an empty Colyseus project", "main": "build/index.ts", "engines": {"node": ">= 16.13.0"}, "scripts": {"start": "ts-node-dev --respawn --transpile-only index.ts", "init-db": "node scripts/init-database.js", "loadtest": "tsx loadtest/example.ts --room my_room --numClients 2", "build": "npm run clean && tsc", "clean": "<PERSON><PERSON><PERSON> build", "test": "mocha -r tsx test/**_test.ts --exit --timeout 15000"}, "author": "", "license": "UNLICENSED", "bugs": {"url": "https://github.com/colyseus/create-colyseus/issues"}, "homepage": "https://github.com/colyseus/create-colyseus#readme", "devDependencies": {"@colyseus/loadtest": "^0.15.0", "@colyseus/testing": "^0.15.0", "@types/cors": "2.8.6", "@types/express": "4.17.1", "@types/mocha": "10.0.1", "@types/node-cron": "3.0.9", "@types/p2": "0.7.40", "copyfiles": "2.4.1", "mocha": "10.2.0", "rimraf": "5.0.0", "tsx": "3.12.6", "ts-node": "^10.7.0", "ts-node-dev": "^1.0.0-pre.63", "typescript": "5.0.4"}, "dependencies": {"@colyseus/auth": "^0.15.10", "@colyseus/monitor": "^0.15.0", "@colyseus/tools": "^0.15.0", "colyseus": "^0.15.0", "cors": "2.8.5", "express": "4.18.2", "mysql2": "3.2.0", "node-cron": "3.0.2", "p2": "0.7.1"}}