#!/usr/bin/env node

/**
 * JiGS Database Initialization Script
 * 
 * This script initializes the database with the minimum required tables
 * for the JiGS RPG engine to function.
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Import database configuration
const dbConfig = require('../src/services/db.ts').default;

async function initializeDatabase() {
  let connection;
  
  try {
    console.log('🚀 Starting JiGS Database Initialization...');
    console.log(`📊 Connecting to database: ${dbConfig.database} on ${dbConfig.host}`);
    
    // Create connection
    connection = await mysql.createConnection({
      host: dbConfig.host,
      user: dbConfig.user,
      password: dbConfig.password,
      database: dbConfig.database,
      multipleStatements: true
    });

    console.log('✅ Connected to MySQL database');

    // Read SQL file
    const sqlFilePath = path.join(__dirname, '../database/init.sql');
    
    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`SQL file not found: ${sqlFilePath}`);
    }

    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log('📄 SQL file loaded');

    // Execute SQL
    console.log('⚙️  Executing database initialization...');
    const [results] = await connection.execute(sqlContent);
    
    console.log('✅ Database initialized successfully!');
    
    // Test the connection by running the getRooms query
    console.log('🧪 Testing database with sample query...');
    const [testResults] = await connection.execute(`
      SELECT
        node.type,
        node.nid,
        node__field_city.field_city_target_id,
        node_field_data.title,
        node__field_tiled.field_tiled_value
      FROM node
      LEFT JOIN node__field_city ON node__field_city.entity_id = node.nid
      LEFT JOIN node_field_data ON node_field_data.nid = node__field_city.field_city_target_id
      LEFT JOIN node__field_tiled ON node__field_tiled.entity_id = node.nid
      WHERE node.type = 'map_grid'
    `);

    if (testResults.length > 0) {
      console.log('✅ Test query successful! Found rooms:');
      testResults.forEach(room => {
        console.log(`   - Room ${room.nid}: ${room.title || 'Untitled'} (Tiled: ${room.field_tiled_value})`);
      });
    } else {
      console.log('⚠️  No rooms found, but database structure is ready');
    }

    console.log('\n🎉 JiGS Database initialization complete!');
    console.log('💡 You can now start the server with: npm start');

  } catch (error) {
    console.error('❌ Database initialization failed:');
    console.error(error.message);
    
    if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 Database does not exist. Please create it first:');
      console.log(`   CREATE DATABASE ${dbConfig.database};`);
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Access denied. Please check your database credentials in:');
      console.log('   server/src/services/db.ts');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the initialization
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
