{"compilerOptions": {"outDir": "build", "target": "ESNext", "module": "CommonJS", "moduleResolution": "node", "strict": true, "allowJs": true, "strictNullChecks": false, "esModuleInterop": true, "experimentalDecorators": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": false}, "include": ["src/**/*.ts", "./environment.d.ts"], "exclude": ["node_modules"]}